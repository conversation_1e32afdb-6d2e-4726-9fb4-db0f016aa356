# API Documentation

- [useMediaQuery](#usemediaquery)

# useMediaQuery

API reference docs for the React useMediaQuery hook. Learn about the parameters, return values, and usage patterns.

## Import

To use the `useMediaQuery` hook, you can choose to import it directly or through the main entry point.

```jsx
import { useMediaQuery } from '@hxnova/react-components';
// or
import { useMediaQuery } from '@hxnova/react-components/utils';
```

## Signature

```tsx
function useMediaQuery(query: string): boolean
```

## Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| **query** | `string` | The media query string to evaluate. Can be a CSS media query or use <PERSON>'s breakpoint helpers. |

## Returns

| Type | Description |
| ---- | ----------- |
| `boolean` | Returns `true` if the media query matches the current viewport, `false` otherwise. |

## Usage with Nova Breakpoints

**Important:** With PigmentCSS, the traditional MUI pattern of `useTheme().breakpoints.up('sm')` doesn't work because `useTheme()` only provides static CSS variables. Nova's `useMediaQuery` hook solves this by providing direct access to Nova's breakpoint system.

### Available Breakpoints

Nova uses the following breakpoints:

- `xs`: 0px (Mobile)
- `sm`: 600px (Tablet Portrait)
- `md`: 840px (Tablet Landscape)
- `lg`: 1200px (Laptop Display)
- `xl`: 1600px (Desktop Display)

### Breakpoint Methods

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

// Screen size queries
const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));

// Specific breakpoint
const isExactlyMd = useMediaQuery(NovaTheme.breakpoints.only('md'));
```

### Custom Media Queries

You can also use custom CSS media queries:

```tsx
const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
const isLandscape = useMediaQuery('(orientation: landscape)');
const isHighDensity = useMediaQuery('(min-resolution: 2dppx)');
const isDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
```

## Migration from MUI Pattern

**❌ Old pattern (doesn't work with PigmentCSS):**
```tsx
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

const theme = useTheme();
const matches = useMediaQuery(theme.breakpoints.up('sm'));
```

**✅ New pattern (works with PigmentCSS):**
```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const matches = useMediaQuery(NovaTheme.breakpoints.up('sm'));
```

## Examples

### Responsive Component

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

function ResponsiveComponent() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
  
  return (
    <div>
      {isMobile && <MobileLayout />}
      {isDesktop && <DesktopLayout />}
    </div>
  );
}
```

### Conditional Styling

```tsx
function StyledComponent() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  
  return (
    <Box
      sx={{
        padding: isMobile ? 1 : 3,
        fontSize: isMobile ? '14px' : '16px',
      }}
    >
      Content
    </Box>
  );
}
```
