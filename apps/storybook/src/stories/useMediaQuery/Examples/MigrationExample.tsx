import React from 'react';
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

export default function MigrationExample() {
  // ❌ Old pattern (doesn't work with PigmentCSS)
  // const theme = useTheme();
  // const matches = useMediaQuery(theme.breakpoints.up('sm'));

  // ✅ New pattern (works with PigmentCSS)
  const matches = useMediaQuery(NovaTheme.breakpoints.up('sm'));

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>
      <Typography variant="titleLarge">Migration Example</Typography>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">Screen is SM or larger: {matches ? '✅ Yes' : '❌ No'}</Typography>

          <Box sx={(theme) => ({ mt: 2, p: 2, backgroundColor: theme.vars.palette.surfaceVariant, borderRadius: 1 })}>
            <Typography variant="labelSmall" sx={{ fontFamily: 'monospace' }}>
              {`// ❌ Old pattern (doesn't work with PigmentCSS)
// const theme = useTheme();
// const matches = useMediaQuery(theme.breakpoints.up('sm'));

// ✅ New pattern (works with PigmentCSS)
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const matches = useMediaQuery(NovaTheme.breakpoints.up('sm'));`}
            </Typography>
          </Box>
        </Card.Content>
      </Card.Root>
    </Box>
  );
}
