import React from 'react';
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

export default function ResponsiveBreakpointsExample() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
  const isLandscape = useMediaQuery('(orientation: landscape)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>
      <Typography variant="titleLarge">Responsive Breakpoints</Typography>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">
            <strong>Current Device:</strong> {isMobile && 'Mobile'}
            {isTablet && 'Tablet'}
            {isDesktop && 'Desktop'}
          </Typography>

          <Typography variant="bodySmall" sx={{ mt: 1 }}>
            Resize your browser window to see the changes
          </Typography>
        </Card.Content>
      </Card.Root>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">
            <strong>Breakpoint Status:</strong>
          </Typography>
          <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography variant="bodySmall">Mobile (down sm): {isMobile ? '✅' : '❌'}</Typography>
            <Typography variant="bodySmall">Tablet (sm-md): {isTablet ? '✅' : '❌'}</Typography>
            <Typography variant="bodySmall">Desktop (up lg): {isDesktop ? '✅' : '❌'}</Typography>
          </Box>
        </Card.Content>
      </Card.Root>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">
            <strong>Other Media Queries:</strong>
          </Typography>
          <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography variant="bodySmall">Landscape: {isLandscape ? '✅' : '❌'}</Typography>
            <Typography variant="bodySmall">Prefers Reduced Motion: {prefersReducedMotion ? '✅' : '❌'}</Typography>
          </Box>
        </Card.Content>
      </Card.Root>

      {isMobile && (
        <Card.Root sx={(theme) => ({ backgroundColor: theme.vars.palette.primary })}>
          <Card.Content>
            <Typography variant="bodyMedium" sx={(theme) => ({ color: theme.vars.palette.onPrimary })}>
              📱 Mobile-only content
            </Typography>
          </Card.Content>
        </Card.Root>
      )}

      {isDesktop && (
        <Card.Root sx={(theme) => ({ backgroundColor: theme.vars.palette.secondary })}>
          <Card.Content>
            <Typography variant="bodyMedium" sx={(theme) => ({ color: theme.vars.palette.onSecondary })}>
              🖥️ Desktop-only content
            </Typography>
          </Card.Content>
        </Card.Root>
      )}
    </Box>
  );
}
