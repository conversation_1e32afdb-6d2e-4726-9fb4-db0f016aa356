import type { Meta, StoryObj } from '@storybook/react';
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

const meta: Meta = {
  title: '@hxnova/react-components/Utils/useMediaQuery',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The useMediaQuery hook provides a way to conditionally apply styles or render different content based on media queries.

**Important:** With PigmentCSS, the traditional MUI pattern of \`useTheme().breakpoints.up('sm')\` doesn't work because \`useTheme()\` only provides static CSS variables. Nova's \`useMediaQuery\` hook solves this by providing direct access to Nova's breakpoint system.

## Usage

\`\`\`tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
\`\`\`
        `,
      },
    },
  },
};

export default meta;
type Story = StoryObj;

function ResponsiveExample() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
  const isLandscape = useMediaQuery('(orientation: landscape)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>
      <Typography variant="titleLarge">Responsive Breakpoints</Typography>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">
            <strong>Current Device:</strong> {isMobile && 'Mobile'}
            {isTablet && 'Tablet'}
            {isDesktop && 'Desktop'}
          </Typography>

          <Typography variant="bodySmall" sx={{ mt: 1 }}>
            Resize your browser window to see the changes
          </Typography>
        </Card.Content>
      </Card.Root>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">
            <strong>Breakpoint Status:</strong>
          </Typography>
          <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography variant="bodySmall">Mobile (down sm): {isMobile ? '✅' : '❌'}</Typography>
            <Typography variant="bodySmall">Tablet (sm-md): {isTablet ? '✅' : '❌'}</Typography>
            <Typography variant="bodySmall">Desktop (up lg): {isDesktop ? '✅' : '❌'}</Typography>
          </Box>
        </Card.Content>
      </Card.Root>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">
            <strong>Other Media Queries:</strong>
          </Typography>
          <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography variant="bodySmall">Landscape: {isLandscape ? '✅' : '❌'}</Typography>
            <Typography variant="bodySmall">Prefers Reduced Motion: {prefersReducedMotion ? '✅' : '❌'}</Typography>
          </Box>
        </Card.Content>
      </Card.Root>

      {isMobile && (
        <Card.Root sx={{ backgroundColor: (theme) => theme.vars.palette.primary }}>
          <Card.Content>
            <Typography variant="bodyMedium" sx={{ color: (theme) => theme.vars.palette.onPrimary }}>
              📱 Mobile-only content
            </Typography>
          </Card.Content>
        </Card.Root>
      )}

      {isDesktop && (
        <Card.Root sx={{ backgroundColor: (theme) => theme.vars.palette.secondary }}>
          <Card.Content>
            <Typography variant="bodyMedium" sx={{ color: (theme) => theme.vars.palette.onSecondary }}>
              🖥️ Desktop-only content
            </Typography>
          </Card.Content>
        </Card.Root>
      )}
    </Box>
  );
}

export const ResponsiveBreakpoints: Story = {
  render: () => <ResponsiveExample />,
  parameters: {
    docs: {
      description: {
        story: `
This example demonstrates how to use the useMediaQuery hook with Nova's breakpoints to create responsive components.

The component shows:
- Current device type based on screen size
- Status of different breakpoint queries
- Conditional rendering based on screen size
- Custom media queries for orientation and accessibility preferences

Try resizing your browser window to see the breakpoints in action!
        `,
      },
    },
  },
};

function MigrationExample() {
  // ❌ Old pattern (doesn't work with PigmentCSS)
  // const theme = useTheme();
  // const matches = useMediaQuery(theme.breakpoints.up('sm'));

  // ✅ New pattern (works with PigmentCSS)
  const matches = useMediaQuery(NovaTheme.breakpoints.up('sm'));

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>
      <Typography variant="titleLarge">Migration Example</Typography>

      <Card.Root>
        <Card.Content>
          <Typography variant="bodyMedium">Screen is SM or larger: {matches ? '✅ Yes' : '❌ No'}</Typography>

          <Box sx={{ mt: 2, p: 2, backgroundColor: (theme) => theme.vars.palette.surfaceVariant, borderRadius: 1 }}>
            <Typography variant="labelSmall" sx={{ fontFamily: 'monospace' }}>
              {`// ❌ Old pattern (doesn't work with PigmentCSS)
// const theme = useTheme();
// const matches = useMediaQuery(theme.breakpoints.up('sm'));

// ✅ New pattern (works with PigmentCSS)
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const matches = useMediaQuery(NovaTheme.breakpoints.up('sm'));`}
            </Typography>
          </Box>
        </Card.Content>
      </Card.Root>
    </Box>
  );
}

export const MigrationFromMUI: Story = {
  render: () => <MigrationExample />,
  parameters: {
    docs: {
      description: {
        story: `
This example shows how to migrate from the traditional MUI useMediaQuery pattern to Nova's approach.

**Why the change?**
With PigmentCSS, \`useTheme()\` only provides static CSS variables and does not include runtime breakpoint helpers. This means the traditional pattern of \`useTheme().breakpoints.up('sm')\` no longer works.

**Solution:**
Import the static \`NovaTheme\` directly and use it with Nova's \`useMediaQuery\` hook.
        `,
      },
    },
  },
};
