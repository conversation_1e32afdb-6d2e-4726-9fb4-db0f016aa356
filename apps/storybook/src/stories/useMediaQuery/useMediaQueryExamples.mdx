import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import ResponsiveBreakpointsExample from './Examples/ResponsiveBreakpointsExample';
import ResponsiveBreakpointsExampleSource from './Examples/ResponsiveBreakpointsExample.tsx?raw';
import MigrationExample from './Examples/MigrationExample';
import MigrationExampleSource from './Examples/MigrationExample.tsx?raw';

<Meta title="@hxnova/react-components/Utils/useMediaQuery/Examples" />

## Responsive Breakpoints

The useMediaQuery hook provides a way to conditionally apply styles or render different content based on media queries. This example demonstrates how to use the hook with <PERSON>'s breakpoints to create responsive components.

**Important:** With PigmentCSS, the traditional MUI pattern of `useTheme().breakpoints.up('sm')` doesn't work because `useTheme()` only provides static CSS variables. Nova's `useMediaQuery` hook solves this by providing direct access to <PERSON>'s breakpoint system.

The component shows:
- Current device type based on screen size
- Status of different breakpoint queries
- Conditional rendering based on screen size
- Custom media queries for orientation and accessibility preferences

Try resizing your browser window to see the breakpoints in action!

<div className="sb-unstyled">
  <ResponsiveBreakpointsExample />
</div>
<CodeExpand code={ ResponsiveBreakpointsExampleSource } showBorderTop style={{marginTop: 16}}/>

## Migration from MUI Pattern

This example shows how to migrate from the traditional MUI useMediaQuery pattern to Nova's approach.

**Why the change?**
With PigmentCSS, `useTheme()` only provides static CSS variables and does not include runtime breakpoint helpers. This means the traditional pattern of `useTheme().breakpoints.up('sm')` no longer works.

**Solution:**
Import the static `NovaTheme` directly and use it with Nova's `useMediaQuery` hook.

<div className="sb-unstyled">
  <MigrationExample />
</div>
<CodeExpand code={ MigrationExampleSource } showBorderTop style={{marginTop: 16}}/>

## Usage

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
```

### Available Breakpoints

Nova uses the following breakpoints:

- `xs`: 0px (Mobile)
- `sm`: 600px (Tablet Portrait)
- `md`: 840px (Tablet Landscape)
- `lg`: 1200px (Laptop Display)
- `xl`: 1600px (Desktop Display)

### Common Patterns

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

function ResponsiveComponent() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
  
  // Custom media queries
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
  const isHighDensity = useMediaQuery('(min-resolution: 2dppx)');
  
  return (
    <div>
      {isMobile && <MobileLayout />}
      {isTablet && <TabletLayout />}
      {isDesktop && <DesktopLayout />}
    </div>
  );
}
```
