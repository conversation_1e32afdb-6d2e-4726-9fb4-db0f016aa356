# useMediaQuery Hook

A pre-configured `useMediaQuery` hook for Nova applications that provides consistent responsive behavior across Nova components.

## Why use Nova's useMediaQuery?

With PigmentCSS, the traditional MUI pattern of using `useTheme().breakpoints.up('sm')` doesn't work because `useTheme()` only provides static CSS variables and does not include runtime breakpoint helpers.

Nova's `useMediaQuery` hook solves this by providing direct access to Nova's breakpoint system for runtime media queries.

## Usage

### Basic Usage

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

function MyComponent() {
  // Check if screen is medium or larger
  const isMdUp = useMediaQuery(NovaTheme.breakpoints.up('md'));
  
  // Check if screen is small or smaller  
  const isSmDown = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  
  // Custom media query
  const isLandscape = useMediaQuery('(orientation: landscape)');
  
  return (
    <div>
      {isMdUp ? 'Desktop view' : 'Mobile view'}
    </div>
  );
}
```

### Migration from MUI Pattern

**❌ Old pattern (doesn't work with PigmentCSS):**
```tsx
import { useTheme } from '@mui/material/styles';

const theme = useTheme();
const matches = useMediaQuery(theme.breakpoints.up('sm'));
```

**✅ New pattern (works with PigmentCSS):**
```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const matches = useMediaQuery(NovaTheme.breakpoints.up('sm'));
```

### Available Breakpoints

Nova uses the following breakpoints:

- `xs`: 0px (Mobile)
- `sm`: 600px (Tablet Portrait)
- `md`: 840px (Tablet Landscape)
- `lg`: 1200px (Laptop Display)
- `xl`: 1600px (Desktop Display)

### Common Patterns

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

function ResponsiveComponent() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
    
  return (
    <div>
      {isMobile && <MobileLayout />}
      {isTablet && <TabletLayout />}
      {isDesktop && <DesktopLayout />}
    </div>
  );
}
```

## API

The hook accepts the same parameters as MUI's `useMediaQuery`:

```tsx
useMediaQuery(query: string, options?: {
  defaultMatches?: boolean;
  matchMedia?: (query: string) => MediaQueryList;
  noSsr?: boolean;
  ssrMatchMedia?: (query: string) => { matches: boolean };
})
```

## SSR Considerations

For server-side rendering, you may want to provide default matches:

```tsx
const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'), {
  defaultMatches: false, // Assume desktop by default
  noSsr: true // Skip SSR hydration mismatch warnings
});
```
