import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';

/**
 * A pre-configured useMediaQuery hook for Nova applications.
 *
 * This hook is configured to work with Nova's breakpoints and provides
 * a consistent way to handle responsive behavior across Nova applications.
 *
 * Note: With PigmentCSS, useTheme() only provides static CSS variables and
 * does not include runtime breakpoint helpers. This utility provides access
 * to Nova's breakpoint system for use with useMediaQuery.
 *
 * @example
 * ```tsx
 * import { useMediaQuery } from '@hxnova/react-components';
 * import { NovaTheme } from '@hxnova/themes';
 *
 * function MyComponent() {
 *   // Check if screen is medium or larger
 *   const isMdUp = useMediaQuery(NovaTheme.breakpoints.up('md'));
 *
 *   // Check if screen is small or smaller
 *   const isSmDown = useMediaQuery(NovaTheme.breakpoints.down('sm'));
 *
 *   // Custom media query
 *   const isLandscape = useMediaQuery('(orientation: landscape)');
 *
 *   return (
 *     <div>
 *       {isMdUp ? 'Desktop view' : 'Mobile view'}
 *     </div>
 *   );
 * }
 * ```
 */
export const useMediaQuery = createUseMediaQuery();
